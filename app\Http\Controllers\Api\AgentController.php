<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class AgentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $agents = Agent::with(['leads', 'properties', 'campaigns'])->get();
        return response()->json($agents);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:agents,email',
            'phone' => 'nullable|string|max:20',
            'license_number' => 'nullable|string|unique:agents,license_number',
            'bio' => 'nullable|string',
            'profile_image' => 'nullable|string',
            'specialties' => 'nullable|array',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'status' => 'nullable|in:active,inactive,suspended',
            'joined_at' => 'nullable|date',
        ]);

        $agent = Agent::create($validated);
        return response()->json($agent, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Agent $agent): JsonResponse
    {
        $agent->load(['leads', 'properties', 'campaigns']);
        return response()->json($agent);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Agent $agent): JsonResponse
    {
        $validated = $request->validate([
            'first_name' => 'sometimes|required|string|max:255',
            'last_name' => 'sometimes|required|string|max:255',
            'email' => ['sometimes', 'required', 'email', Rule::unique('agents')->ignore($agent->id)],
            'phone' => 'nullable|string|max:20',
            'license_number' => ['nullable', 'string', Rule::unique('agents')->ignore($agent->id)],
            'bio' => 'nullable|string',
            'profile_image' => 'nullable|string',
            'specialties' => 'nullable|array',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'status' => 'nullable|in:active,inactive,suspended',
            'joined_at' => 'nullable|date',
        ]);

        $agent->update($validated);
        return response()->json($agent);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Agent $agent): JsonResponse
    {
        $agent->delete();
        return response()->json(['message' => 'Agent deleted successfully']);
    }
}
