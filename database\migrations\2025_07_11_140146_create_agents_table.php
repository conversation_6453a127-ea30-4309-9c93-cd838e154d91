<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agents', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('license_number')->unique()->nullable();
            $table->text('bio')->nullable();
            $table->string('profile_image')->nullable();
            $table->json('specialties')->nullable(); // e.g., ["Residential", "Commercial", "Luxury"]
            $table->decimal('commission_rate', 5, 2)->default(3.00); // percentage
            $table->string('status')->default('active'); // active, inactive, suspended
            $table->timestamp('joined_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agents');
    }
};
