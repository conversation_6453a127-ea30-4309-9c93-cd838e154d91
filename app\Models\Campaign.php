<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Campaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'agent_id',
        'name',
        'description',
        'type',
        'status',
        'budget',
        'spent',
        'target_audience_size',
        'impressions',
        'clicks',
        'conversions',
        'start_date',
        'end_date',
        'metrics',
    ];

    protected $casts = [
        'budget' => 'decimal:2',
        'spent' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'metrics' => 'array',
    ];

    public function agent(): BelongsTo
    {
        return $this->belongsTo(Agent::class);
    }

    public function getClickThroughRateAttribute(): float
    {
        return $this->impressions > 0 ? ($this->clicks / $this->impressions) * 100 : 0;
    }

    public function getConversionRateAttribute(): float
    {
        return $this->clicks > 0 ? ($this->conversions / $this->clicks) * 100 : 0;
    }

    public function getRemainingBudgetAttribute(): float
    {
        return $this->budget - $this->spent;
    }
}
