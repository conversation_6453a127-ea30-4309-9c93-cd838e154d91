<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AgentController;
use App\Http\Controllers\Api\LeadController;
use App\Http\Controllers\Api\PropertyController;
use App\Http\Controllers\Api\CampaignController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API routes (for now, we'll add authentication later)
Route::apiResource('agents', AgentController::class);
Route::apiResource('leads', LeadController::class);
Route::apiResource('properties', PropertyController::class);
Route::apiResource('campaigns', CampaignController::class);

// Additional routes for specific functionality
Route::get('agents/{agent}/leads', [AgentController::class, 'leads']);
Route::get('agents/{agent}/properties', [AgentController::class, 'properties']);
Route::get('agents/{agent}/campaigns', [AgentController::class, 'campaigns']);
