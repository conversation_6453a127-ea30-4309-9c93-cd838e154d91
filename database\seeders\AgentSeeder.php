<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Agent;
use Carbon\Carbon;

class AgentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $agents = [
            [
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'phone' => '+961-1-234567',
                'license_number' => 'LB001234',
                'bio' => 'Experienced real estate agent specializing in luxury properties in Beirut.',
                'specialties' => ['Luxury', 'Residential', 'Commercial'],
                'commission_rate' => 3.5,
                'status' => 'active',
                'joined_at' => Carbon::now()->subMonths(6),
            ],
            [
                'first_name' => 'Ahmad',
                'last_name' => 'Khalil',
                'email' => '<EMAIL>',
                'phone' => '+961-1-345678',
                'license_number' => 'LB002345',
                'bio' => 'Dedicated agent focusing on residential properties and first-time buyers.',
                'specialties' => ['Residential', 'First-time Buyers'],
                'commission_rate' => 3.0,
                'status' => 'active',
                'joined_at' => Carbon::now()->subMonths(12),
            ],
            [
                'first_name' => 'Maya',
                'last_name' => 'Abou Zeid',
                'email' => '<EMAIL>',
                'phone' => '+961-1-456789',
                'license_number' => 'LB003456',
                'bio' => 'Commercial real estate specialist with expertise in office buildings and retail spaces.',
                'specialties' => ['Commercial', 'Office Buildings', 'Retail'],
                'commission_rate' => 4.0,
                'status' => 'active',
                'joined_at' => Carbon::now()->subMonths(18),
            ],
        ];

        foreach ($agents as $agentData) {
            Agent::create($agentData);
        }
    }
}
